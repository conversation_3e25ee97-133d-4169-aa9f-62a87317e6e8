'use client';

import { Header } from '@/components/ui/header';
import { SidebarNavigation } from '@/components/ui/sidebar-navigation';
import { useAuth } from '@/lib/auth';
import { usePathname } from 'next/navigation';
import { ReactNode } from 'react';

interface LayoutWrapperProps {
  children: ReactNode;
}

// Define public routes that don't require authentication
const publicRoutes = [
  '/',
  '/features',
  '/pricing', 
  '/about',
  '/contact',
  '/auth/login',
  '/auth/register',
  '/auth/forgot-password',
  '/auth/reset-password'
];

export function LayoutWrapper({ children }: LayoutWrapperProps) {
  const { isAuthenticated, isLoading } = useAuth();
  const pathname = usePathname();
  
  // Check if current route is public
  const isPublicRoute = publicRoutes.some(route => 
    pathname === route || (route !== '/' && pathname.startsWith(route))
  );

  // Show loading state during auth check
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-hero flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  // Public layout (no sidebar, public header)
  if (isPublicRoute || !isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-hero">
        <Header />
        <main className="min-h-screen">
          {children}
        </main>
      </div>
    );
  }

  // Authenticated layout (with sidebar, no header since sidebar contains user menu)
  return (
    <div className="min-h-screen bg-gradient-hero">
      <SidebarNavigation />
      <div className="lg:ml-64 min-h-screen">
        <main className="min-h-screen">
          <div className="container-app section-padding">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
