'use client';

import { cn } from '@/lib/utils';
import { forwardRef, HTMLAttributes, ReactNode } from 'react';

// Base Card Component
interface CardProps extends HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'compact' | 'large' | 'interactive' | 'elevated';
  children: ReactNode;
}

const Card = ({ className, variant = 'default', children, ...props }: CardProps) => {
  const baseClasses = 'card';
  const variantClasses = {
    default: 'card-default',
    compact: 'card-compact',
    large: 'card-large',
    interactive: 'card-interactive hover-lift',
    elevated: 'card-default shadow-xl hover:shadow-2xl',
  };

  return (
    <div
      className={cn(baseClasses, variantClasses[variant], className)}
      {...props}
    >
      {children}
    </div>
  );
};

// Card Header Component
interface CardHeaderProps extends HTMLAttributes<HTMLDivElement> {
  children: ReactNode;
}

const CardHeader = forwardRef<HTMLDivElement, CardHeaderProps>(
  ({ className, children, ...props }, ref) => (
    <div ref={ref} className={cn('card-header', className)} {...props}>
      {children}
    </div>
  )
);
CardHeader.displayName = 'CardHeader';

// Card Title Component
interface CardTitleProps extends HTMLAttributes<HTMLHeadingElement> {
  children: ReactNode;
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
}

const CardTitle = forwardRef<HTMLHeadingElement, CardTitleProps>(
  ({ className, children, as: Component = 'h3', ...props }, ref) => (
    <Component ref={ref} className={cn('card-title', className)} {...props}>
      {children}
    </Component>
  )
);
CardTitle.displayName = 'CardTitle';

// Card Description Component
interface CardDescriptionProps extends HTMLAttributes<HTMLParagraphElement> {
  children: ReactNode;
}

const CardDescription = forwardRef<HTMLParagraphElement, CardDescriptionProps>(
  ({ className, children, ...props }, ref) => (
    <p ref={ref} className={cn('card-description', className)} {...props}>
      {children}
    </p>
  )
);
CardDescription.displayName = 'CardDescription';

// Card Content Component
interface CardContentProps extends HTMLAttributes<HTMLDivElement> {
  children: ReactNode;
}

const CardContent = ({ className, children, ...props }: CardContentProps) => (
  <div className={cn('card-content', className)} {...props}>
    {children}
  </div>
);

// Card Footer Component
interface CardFooterProps extends HTMLAttributes<HTMLDivElement> {
  children: ReactNode;
}

const CardFooter = forwardRef<HTMLDivElement, CardFooterProps>(
  ({ className, children, ...props }, ref) => (
    <div ref={ref} className={cn('card-footer', className)} {...props}>
      {children}
    </div>
  )
);
CardFooter.displayName = 'CardFooter';

// Specialized Card Components

// Metric Card for displaying key metrics
interface MetricCardProps {
  title: string;
  value: string | number;
  change?: {
    value: number;
    type: 'increase' | 'decrease' | 'neutral';
  };
  icon?: ReactNode;
  className?: string;
}

const MetricCard = ({ title, value, change, icon, className }: MetricCardProps) => {
  const getChangeColor = (type: 'increase' | 'decrease' | 'neutral') => {
    switch (type) {
      case 'increase':
        return 'text-green-600 bg-green-50';
      case 'decrease':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <Card variant="compact" className={cn('relative overflow-hidden', className)}>
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
            {title}
          </p>
          <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {value}
          </p>
          {change && (
            <div className={cn('inline-flex items-center px-2 py-1 rounded-full text-xs font-medium mt-2', getChangeColor(change.type))}>
              {change.type === 'increase' ? '↗' : change.type === 'decrease' ? '↘' : '→'} {Math.abs(change.value)}%
            </div>
          )}
        </div>
        {icon && (
          <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center text-white">
            {icon}
          </div>
        )}
      </div>
    </Card>
  );
};

// Feature Card for showcasing features
interface FeatureCardProps {
  title: string;
  description: string;
  icon?: ReactNode;
  href?: string;
  onClick?: () => void;
  className?: string;
}

const FeatureCard = ({ title, description, icon, href, onClick, className }: FeatureCardProps) => {
  const CardComponent = href ? 'a' : 'div';
  const isInteractive = href || onClick;

  return (
    <Card 
      variant={isInteractive ? 'interactive' : 'default'} 
      className={cn('text-center', className)}
      as={CardComponent}
      href={href}
      onClick={onClick}
    >
      {icon && (
        <div className="w-16 h-16 bg-gradient-to-br from-primary-100 to-primary-200 dark:from-primary-800 dark:to-primary-700 rounded-2xl flex items-center justify-center mx-auto mb-4">
          <div className="text-primary-600 dark:text-primary-300">
            {icon}
          </div>
        </div>
      )}
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <CardDescription>{description}</CardDescription>
      </CardContent>
    </Card>
  );
};

// Status Card for displaying status information
interface StatusCardProps {
  title: string;
  status: 'success' | 'warning' | 'error' | 'info';
  message: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  className?: string;
}

const StatusCard = ({ title, status, message, action, className }: StatusCardProps) => {
  const statusConfig = {
    success: {
      bgColor: 'bg-green-50 dark:bg-green-900/20',
      borderColor: 'border-green-200 dark:border-green-800',
      iconColor: 'text-green-600 dark:text-green-400',
      icon: '✓',
    },
    warning: {
      bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',
      borderColor: 'border-yellow-200 dark:border-yellow-800',
      iconColor: 'text-yellow-600 dark:text-yellow-400',
      icon: '⚠',
    },
    error: {
      bgColor: 'bg-red-50 dark:bg-red-900/20',
      borderColor: 'border-red-200 dark:border-red-800',
      iconColor: 'text-red-600 dark:text-red-400',
      icon: '✕',
    },
    info: {
      bgColor: 'bg-blue-50 dark:bg-blue-900/20',
      borderColor: 'border-blue-200 dark:border-blue-800',
      iconColor: 'text-blue-600 dark:text-blue-400',
      icon: 'ℹ',
    },
  };

  const config = statusConfig[status];

  return (
    <Card className={cn(config.bgColor, config.borderColor, className)}>
      <CardContent className="flex items-start space-x-3">
        <div className={cn('w-6 h-6 rounded-full flex items-center justify-center text-sm font-bold', config.iconColor)}>
          {config.icon}
        </div>
        <div className="flex-1">
          <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-1">{title}</h4>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">{message}</p>
          {action && (
            <button
              onClick={action.onClick}
              className="text-sm font-medium text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300"
            >
              {action.label}
            </button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export {
    Card, CardContent, CardDescription, CardFooter, CardHeader,
    CardTitle, FeatureCard, MetricCard, StatusCard
};

